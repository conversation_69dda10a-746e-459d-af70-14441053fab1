<x-app-layout>
    <x-slot name="title">Choose Your Features</x-slot>
    <x-slot name="subtitle">Select the features that best fit your business needs</x-slot>

    <div class="max-w-4xl mx-auto">
        <div class="space-y-8">
            <!-- Progress Indicator -->
            <div class="flex items-center justify-center space-x-4 mb-8">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                        <i class="fas fa-check"></i>
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-900">Welcome</span>
                </div>
                <div class="w-16 h-1 bg-primary-600 rounded"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                        <i class="fas fa-check"></i>
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-900">Setup</span>
                </div>
                <div class="w-16 h-1 bg-primary-600 rounded"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <span class="ml-2 text-sm font-medium text-primary-600">Features</span>
                </div>
                <div class="w-16 h-1 bg-gray-200 rounded"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-200 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">4</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Complete</span>
                </div>
            </div>

            <!-- Features Selection -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-xl font-semibold text-gray-900">Select Your Business Features</h3>
                    <p class="text-gray-600 mt-1">Choose the features you want to enable for your business</p>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('business.onboarding.features.update', $business) }}" class="space-y-6">
                        @csrf

                        <!-- Core Features -->
                        <div>
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Core Features</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <input type="checkbox" name="features[]" value="invoicing" checked disabled
                                               class="mt-1 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                        <div class="ml-3">
                                            <h5 class="font-medium text-gray-900">Invoice Management</h5>
                                            <p class="text-sm text-gray-600">Create, send, and track invoices</p>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
                                                Required
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <input type="checkbox" name="features[]" value="client_management" checked disabled
                                               class="mt-1 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                        <div class="ml-3">
                                            <h5 class="font-medium text-gray-900">Client Management</h5>
                                            <p class="text-sm text-gray-600">Manage client information and relationships</p>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
                                                Required
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Optional Features -->
                        <div>
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Optional Features</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <input type="checkbox" name="features[]" value="project_management"
                                               class="mt-1 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                        <div class="ml-3">
                                            <h5 class="font-medium text-gray-900">Project Management</h5>
                                            <p class="text-sm text-gray-600">Track projects, tasks, and time</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <input type="checkbox" name="features[]" value="expense_tracking"
                                               class="mt-1 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                        <div class="ml-3">
                                            <h5 class="font-medium text-gray-900">Expense Tracking</h5>
                                            <p class="text-sm text-gray-600">Track and categorize business expenses</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <input type="checkbox" name="features[]" value="contract_management"
                                               class="mt-1 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                        <div class="ml-3">
                                            <h5 class="font-medium text-gray-900">Contract Management</h5>
                                            <p class="text-sm text-gray-600">Create and manage client contracts</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <input type="checkbox" name="features[]" value="lead_management"
                                               class="mt-1 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                        <div class="ml-3">
                                            <h5 class="font-medium text-gray-900">Lead Management</h5>
                                            <p class="text-sm text-gray-600">Track and convert potential clients</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <input type="checkbox" name="features[]" value="proposal_system"
                                               class="mt-1 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                        <div class="ml-3">
                                            <h5 class="font-medium text-gray-900">Proposal System</h5>
                                            <p class="text-sm text-gray-600">Create and send professional proposals</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <input type="checkbox" name="features[]" value="automation"
                                               class="mt-1 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                        <div class="ml-3">
                                            <h5 class="font-medium text-gray-900">Automation</h5>
                                            <p class="text-sm text-gray-600">Automate workflows and notifications</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Features -->
                        <div>
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Advanced Features</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="border border-gray-200 rounded-lg p-4 opacity-75">
                                    <div class="flex items-start">
                                        <input type="checkbox" name="features[]" value="ai_assistant" disabled
                                               class="mt-1 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                        <div class="ml-3">
                                            <h5 class="font-medium text-gray-900">AI Assistant</h5>
                                            <p class="text-sm text-gray-600">AI-powered content generation and insights</p>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-2">
                                                Business Plan
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4 opacity-75">
                                    <div class="flex items-start">
                                        <input type="checkbox" name="features[]" value="advanced_analytics" disabled
                                               class="mt-1 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                        <div class="ml-3">
                                            <h5 class="font-medium text-gray-900">Advanced Analytics</h5>
                                            <p class="text-sm text-gray-600">Detailed business insights and reports</p>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-2">
                                                Business Plan
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="flex justify-between pt-6 border-t border-gray-200">
                            <a href="{{ route('business.onboarding.setup', $business) }}" 
                               class="btn btn-outline">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Back
                            </a>
                            <button type="submit" class="btn btn-primary">
                                Continue
                                <i class="fas fa-arrow-right ml-2"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
