<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'business_id',
        'client_id',
        'recurring_invoice_id',
        'invoice_number',
        'invoice_date',
        'due_date',
        'subtotal',
        'tax_percentage',
        'tax_amount',
        'tds_percentage',
        'tds_amount',
        'total_amount',
        'net_amount',
        'status',
        'paid_date',
        'notes',
        'pdf_path',
        'public_token',
        'payment_token',
        'client_viewed_at',
        'payment_attempts',
    ];

    protected function casts(): array
    {
        return [
            'invoice_date' => 'date',
            'due_date' => 'date',
            'paid_date' => 'date',
            'client_viewed_at' => 'datetime',
            'payment_attempts' => 'array',
            'subtotal' => 'float',
            'tax_percentage' => 'float',
            'tax_amount' => 'float',
            'tds_percentage' => 'float',
            'tds_amount' => 'float',
            'total_amount' => 'float',
            'net_amount' => 'float',
        ];
    }

    /**
     * Get the user that owns the invoice.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the business that owns the invoice.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the client that owns the invoice.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the recurring invoice that generated this invoice.
     */
    public function recurringInvoice(): BelongsTo
    {
        return $this->belongsTo(RecurringInvoice::class);
    }

    /**
     * Get the invoice items for the invoice.
     */
    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * Get the follow-ups for the invoice.
     */
    public function followUps(): HasMany
    {
        return $this->hasMany(FollowUp::class);
    }



    /**
     * Get the TDS record for the invoice.
     */
    public function tdsRecord(): HasOne
    {
        return $this->hasOne(TdsRecord::class);
    }

    /**
     * Get the time entries for the invoice.
     */
    public function timeEntries(): HasMany
    {
        return $this->hasMany(TimeEntry::class);
    }

    /**
     * Generate next invoice number.
     */
    public static function generateInvoiceNumber(): string
    {
        $lastInvoice = static::latest('id')->first();
        $nextNumber = $lastInvoice ? (int) substr($lastInvoice->invoice_number, 4) + 1 : 1;
        return 'INV-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Check if invoice is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status !== 'paid' && $this->due_date < now();
    }

    /**
     * Scope for optimized listing queries.
     */
    public function scopeForListing($query)
    {
        return $query->select([
            'id', 'user_id', 'client_id', 'invoice_number',
            'invoice_date', 'due_date', 'total_amount', 'status', 'created_at'
        ])->with('client:id,name,company_name');
    }

    /**
     * Scope for dashboard queries.
     */
    public function scopeForDashboard($query)
    {
        return $query->select([
            'id', 'client_id', 'total_amount', 'status', 'due_date', 'created_at'
        ]);
    }

    /**
     * Scope for revenue calculations.
     */
    public function scopeForRevenue($query)
    {
        return $query->select(['id', 'total_amount', 'status', 'created_at']);
    }

    /**
     * Get the invoice payments for the invoice.
     */
    public function invoicePayments(): HasMany
    {
        return $this->hasMany(InvoicePayment::class);
    }

    /**
     * Get the completed invoice payments.
     */
    public function completedPayments(): HasMany
    {
        return $this->invoicePayments()->completed();
    }

    /**
     * Generate public token for client access.
     */
    public function generatePublicToken(): string
    {
        $this->public_token = \Illuminate\Support\Str::random(64);
        $this->save();
        return $this->public_token;
    }

    /**
     * Generate payment token for secure payment access.
     */
    public function generatePaymentToken(): string
    {
        $this->payment_token = \Illuminate\Support\Str::random(64);
        $this->save();
        return $this->payment_token;
    }

    /**
     * Get the public URL for client access.
     */
    public function getPublicUrlAttribute(): string
    {
        if (!$this->public_token) {
            $this->generatePublicToken();
        }
        return route('client-portal.invoice.view', [
            'invoice' => $this->id,
            'token' => $this->public_token
        ]);
    }

    /**
     * Get the payment URL for client payments.
     */
    public function getPaymentUrlAttribute(): string
    {
        if (!$this->payment_token) {
            $this->generatePaymentToken();
        }
        return route('client-portal.invoice.payment', [
            'invoice' => $this->id,
            'token' => $this->payment_token
        ]);
    }

    /**
     * Mark invoice as viewed by client.
     */
    public function markAsViewedByClient(): void
    {
        if (!$this->client_viewed_at) {
            $this->update(['client_viewed_at' => now()]);
        }
    }

    /**
     * Check if invoice has been viewed by client.
     */
    public function hasBeenViewedByClient(): bool
    {
        return !is_null($this->client_viewed_at);
    }

    /**
     * Get total amount paid by clients.
     */
    public function getTotalPaidAmountAttribute(): float
    {
        return $this->completedPayments()->sum('amount');
    }

    /**
     * Get remaining amount to be paid.
     */
    public function getRemainingAmountAttribute(): float
    {
        return max(0, $this->total_amount - $this->total_paid_amount);
    }

    /**
     * Check if invoice is fully paid by clients.
     */
    public function isFullyPaidByClients(): bool
    {
        return $this->remaining_amount <= 0.01; // Allow for small rounding differences
    }

    /**
     * Record payment attempt.
     */
    public function recordPaymentAttempt(array $attemptData): void
    {
        $attempts = $this->payment_attempts ?? [];
        $attempts[] = array_merge($attemptData, ['attempted_at' => now()]);
        $this->update(['payment_attempts' => $attempts]);
    }

    /**
     * Get payment attempt count for today.
     */
    public function getTodayPaymentAttempts(): int
    {
        if (!$this->payment_attempts) {
            return 0;
        }

        $today = now()->startOfDay();
        return collect($this->payment_attempts)
            ->filter(function ($attempt) use ($today) {
                return \Carbon\Carbon::parse($attempt['attempted_at'])->gte($today);
            })
            ->count();
    }
}
