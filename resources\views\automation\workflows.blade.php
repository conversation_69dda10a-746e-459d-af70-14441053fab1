<x-app-layout>
    <x-slot name="title">Workflow Automation</x-slot>
    <x-slot name="subtitle">Manage your automated workflows and triggers</x-slot>

    <div class="space-y-6">
        <!-- Header Actions -->
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg font-medium text-gray-900">Active Workflows</h3>
                <p class="text-sm text-gray-600">Automate your business processes with custom workflows</p>
            </div>
            <button type="button" 
                    class="btn btn-primary"
                    onclick="openCreateWorkflowModal()">
                <i class="fas fa-plus mr-2"></i>
                Create Workflow
            </button>
        </div>

        <!-- Workflows List -->
        <div class="card">
            <div class="card-body">
                @if($workflows->count() > 0)
                    <div class="space-y-4">
                        @foreach($workflows as $workflow)
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3">
                                            <h4 class="text-lg font-medium text-gray-900">{{ $workflow->name }}</h4>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($workflow->is_active) bg-green-100 text-green-800 @else bg-gray-100 text-gray-800 @endif">
                                                {{ $workflow->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-600 mt-1">{{ $workflow->description }}</p>
                                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                            <span><i class="fas fa-play-circle mr-1"></i>{{ $workflow->executions_count }} executions</span>
                                            <span><i class="fas fa-clock mr-1"></i>Created {{ $workflow->created_at->diffForHumans() }}</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button type="button" 
                                                class="btn btn-sm btn-outline"
                                                onclick="testWorkflow({{ $workflow->id }})">
                                            <i class="fas fa-play mr-1"></i>
                                            Test
                                        </button>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline"
                                                onclick="editWorkflow({{ $workflow->id }})">
                                            <i class="fas fa-edit mr-1"></i>
                                            Edit
                                        </button>
                                        <button type="button" 
                                                class="btn btn-sm {{ $workflow->is_active ? 'btn-warning' : 'btn-success' }}"
                                                onclick="toggleWorkflow({{ $workflow->id }})">
                                            <i class="fas fa-{{ $workflow->is_active ? 'pause' : 'play' }} mr-1"></i>
                                            {{ $workflow->is_active ? 'Pause' : 'Activate' }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <div class="w-24 h-24 mx-auto mb-4 text-gray-300">
                            <i class="fas fa-robot text-6xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No workflows yet</h3>
                        <p class="text-gray-600 mb-6">Create your first workflow to automate your business processes</p>
                        <button type="button" 
                                class="btn btn-primary"
                                onclick="openCreateWorkflowModal()">
                            <i class="fas fa-plus mr-2"></i>
                            Create Your First Workflow
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Create Workflow Modal -->
    <div id="createWorkflowModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Create New Workflow</h3>
                <form id="createWorkflowForm">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                            <input type="text" name="name" class="form-input w-full" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea name="description" rows="3" class="form-input w-full"></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Trigger</label>
                            <select name="trigger_type" class="form-input w-full" required>
                                <option value="">Select trigger...</option>
                                <option value="invoice_overdue">Invoice Overdue</option>
                                <option value="contract_expiring">Contract Expiring</option>
                                <option value="payment_received">Payment Received</option>
                                <option value="client_created">New Client</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" class="btn btn-outline" onclick="closeCreateWorkflowModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Workflow</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openCreateWorkflowModal() {
            document.getElementById('createWorkflowModal').classList.remove('hidden');
        }

        function closeCreateWorkflowModal() {
            document.getElementById('createWorkflowModal').classList.add('hidden');
            document.getElementById('createWorkflowForm').reset();
        }

        function toggleWorkflow(workflowId) {
            fetch(`/automation/workflows/${workflowId}/toggle`, {
                method: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            });
        }

        function testWorkflow(workflowId) {
            fetch(`/automation/workflows/${workflowId}/test`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ test_data: {} })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Workflow test completed successfully!');
                } else {
                    alert('Workflow test failed: ' + data.error);
                }
            });
        }

        function editWorkflow(workflowId) {
            // Implement edit functionality
            alert('Edit functionality coming soon!');
        }

        document.getElementById('createWorkflowForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            fetch('/automation/workflows', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeCreateWorkflowModal();
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            });
        });
    </script>
</x-app-layout>
