<x-frontend-layout>
    <x-slot name="title">Pricing - Freeligo</x-slot>
    <x-slot name="description">Choose the perfect plan for your freelance business. Transparent pricing with no hidden fees. Start with our free trial.</x-slot>

<!-- Hero Section -->
<section class="bg-gradient-to-br from-slate-50 via-emerald-50 to-teal-50 py-16 lg:py-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-gray-900 mb-6">
                Choose the Perfect Plan for
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">Your Business</span>
            </h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed">
                Start with our free plan and upgrade as you grow. All plans include core features with no hidden fees.
            </p>

            <!-- Billing Toggle -->
            <div class="flex items-center justify-center mb-12">
                <span class="text-sm font-medium text-gray-700 mr-3">Monthly</span>
                <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2" role="switch" aria-checked="false">
                    <span class="sr-only">Use setting</span>
                    <span class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out translate-x-0"></span>
                </button>
                <span class="text-sm font-medium text-gray-700 ml-3">
                    Yearly
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 ml-2">
                        Save 20%
                    </span>
                </span>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Cards -->
<section class="py-16 lg:py-24 bg-white relative">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-6 xl:gap-8">
            @foreach($plans as $index => $plan)
            <div class="relative {{ $plan->is_popular ? 'lg:scale-105 lg:z-10' : '' }}">
                @if($plan->is_popular)
                <div class="absolute -top-5 left-1/2 transform -translate-x-1/2 z-20">
                    <div class="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                        <i class="fas fa-star mr-1"></i>
                        Most Popular
                    </div>
                </div>
                @endif

                <div class="bg-white rounded-3xl {{ $plan->is_popular ? 'border-2 border-emerald-500 shadow-2xl' : 'border border-gray-200 shadow-lg hover:shadow-xl' }} p-8 lg:p-10 transition-all duration-300 h-full flex flex-col">
                    <!-- Plan Header -->
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-2xl {{ $plan->is_popular ? 'bg-gradient-to-br from-emerald-500 to-teal-500' : ($plan->price == 0 ? 'bg-gradient-to-br from-gray-400 to-gray-500' : 'bg-gradient-to-br from-blue-500 to-indigo-500') }} flex items-center justify-center">
                            @if($plan->slug === 'free')
                                <i class="fas fa-gift text-white text-2xl"></i>
                            @elseif($plan->slug === 'pro')
                                <i class="fas fa-rocket text-white text-2xl"></i>
                            @else
                                <i class="fas fa-building text-white text-2xl"></i>
                            @endif
                        </div>

                        <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-3">{{ $plan->name }}</h3>
                        <p class="text-gray-600 text-base lg:text-lg leading-relaxed">{{ $plan->description }}</p>
                    </div>

                    <!-- Pricing -->
                    <div class="text-center mb-8">
                        <div class="flex items-baseline justify-center mb-2">
                            @if($plan->price == 0)
                                <span class="text-5xl lg:text-6xl font-bold text-gray-900">Free</span>
                            @else
                                <span class="text-2xl font-semibold text-gray-500">$</span>
                                <span class="text-5xl lg:text-6xl font-bold {{ $plan->is_popular ? 'text-emerald-600' : 'text-gray-900' }}">{{ number_format($plan->price, 0) }}</span>
                                <span class="text-xl font-medium text-gray-500 ml-1">/{{ $plan->billing_cycle }}</span>
                            @endif
                        </div>
                        @if($plan->price > 0)
                            <p class="text-sm text-gray-500">Billed {{ $plan->billing_cycle === 'month' ? 'monthly' : 'annually' }}</p>
                        @endif
                    </div>

                    <!-- CTA Button -->
                    <div class="mb-8">
                        @if($plan->slug === 'free')
                            <a href="{{ route('register') }}" class="w-full bg-gray-900 hover:bg-gray-800 text-white py-4 px-6 rounded-2xl font-semibold transition-all duration-300 block text-center text-lg">
                                Get Started Free
                            </a>
                        @elseif($plan->slug === 'pro')
                            <a href="{{ route('register') }}" class="w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white py-4 px-6 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 block text-center text-lg shadow-lg">
                                <i class="fas fa-crown mr-2"></i>
                                Choose Pro
                            </a>
                        @else
                            <a href="{{ route('contact') }}" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-4 px-6 rounded-2xl font-semibold transition-all duration-300 block text-center text-lg">
                                Contact Sales
                            </a>
                        @endif
                    </div>

                    <!-- Features List -->
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-4 text-lg">Everything in {{ $plan->name }}:</h4>
                        <ul class="space-y-4">
                            @foreach($plan->planFeatures as $feature)
                                @if($feature->feature_type === 'boolean' && $feature->getBooleanValue())
                                    <li class="flex items-start">
                                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-emerald-100 flex items-center justify-center mr-3 mt-0.5">
                                            <i class="fas fa-check text-emerald-600 text-sm"></i>
                                        </div>
                                        <span class="text-gray-700 text-base">{{ $feature->feature_name }}</span>
                                    </li>
                                @elseif($feature->feature_type === 'limit')
                                    <li class="flex items-start">
                                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-emerald-100 flex items-center justify-center mr-3 mt-0.5">
                                            <i class="fas fa-check text-emerald-600 text-sm"></i>
                                        </div>
                                        <span class="text-gray-700 text-base">
                                            <span class="font-medium">
                                                @if($feature->feature_value === 'unlimited')
                                                    Unlimited
                                                @else
                                                    {{ $feature->feature_value }}
                                                @endif
                                            </span>
                                            {{ $feature->feature_name }}
                                        </span>
                                    </li>
                                @elseif($feature->feature_type === 'text')
                                    <li class="flex items-start">
                                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-emerald-100 flex items-center justify-center mr-3 mt-0.5">
                                            <i class="fas fa-check text-emerald-600 text-sm"></i>
                                        </div>
                                        <span class="text-gray-700 text-base">{{ $feature->feature_value }}</span>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Trust Indicators -->
        <div class="mt-16 text-center">
            <p class="text-gray-600 mb-6">Trusted by 10,000+ freelancers and small businesses worldwide</p>
            <div class="flex items-center justify-center space-x-8 opacity-60">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-emerald-600 mr-2"></i>
                    <span class="text-sm text-gray-600">SSL Secured</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-credit-card text-emerald-600 mr-2"></i>
                    <span class="text-sm text-gray-600">Secure Payments</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-undo text-emerald-600 mr-2"></i>
                    <span class="text-sm text-gray-600">Cancel Anytime</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 lg:py-24 bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl lg:text-5xl font-heading font-bold text-gray-900 mb-6">
                Frequently Asked Questions
            </h2>
            <p class="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
                Everything you need to know about our pricing and plans. Can't find what you're looking for?
                <a href="{{ route('contact') }}" class="text-emerald-600 hover:text-emerald-700 font-medium">Contact us</a>.
            </p>
        </div>

        <div x-data="{ openFaq: null }" class="space-y-6">
            <!-- FAQ Item 1 -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <button @click="openFaq = openFaq === 1 ? null : 1" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center mr-4">
                            <i class="fas fa-exchange-alt text-emerald-600 text-sm"></i>
                        </div>
                        <span class="font-semibold text-gray-900 text-lg">Can I change my plan at any time?</span>
                    </div>
                    <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-chevron-down text-gray-500 transform transition-transform" :class="openFaq === 1 ? 'rotate-180' : ''"></i>
                    </div>
                </button>
                <div x-show="openFaq === 1" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" class="px-8 pb-6">
                    <p class="text-gray-600 text-base leading-relaxed">Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle, and we'll prorate any differences.</p>
                </div>
            </div>

            <!-- FAQ Item 2 -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <button @click="openFaq = openFaq === 2 ? null : 2" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center mr-4">
                            <i class="fas fa-gift text-emerald-600 text-sm"></i>
                        </div>
                        <span class="font-semibold text-gray-900 text-lg">What payment methods do you accept?</span>
                    </div>
                    <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-chevron-down text-gray-500 transform transition-transform" :class="openFaq === 2 ? 'rotate-180' : ''"></i>
                    </div>
                </button>
                <div x-show="openFaq === 2" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" class="px-8 pb-6">
                    <p class="text-gray-600 text-base leading-relaxed">We accept all major credit cards (Visa, MasterCard, American Express) and PayPal. All payments are processed securely through our encrypted payment system.</p>
                </div>
            </div>

            <!-- FAQ Item 3 -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <button @click="openFaq = openFaq === 3 ? null : 3" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-emerald-600 text-sm"></i>
                        </div>
                        <span class="font-semibold text-gray-900 text-lg">Is my data safe with Freeligo?</span>
                    </div>
                    <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-chevron-down text-gray-500 transform transition-transform" :class="openFaq === 3 ? 'rotate-180' : ''"></i>
                    </div>
                </button>
                <div x-show="openFaq === 3" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" class="px-8 pb-6">
                    <p class="text-gray-600 text-base leading-relaxed">Absolutely! We use bank-level encryption and security measures to protect your data. Your information is stored securely and we never share it with third parties.</p>
                </div>
            </div>

            <!-- FAQ Item 4 -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <button @click="openFaq = openFaq === 4 ? null : 4" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center mr-4">
                            <i class="fas fa-times-circle text-emerald-600 text-sm"></i>
                        </div>
                        <span class="font-semibold text-gray-900 text-lg">Can I cancel my subscription?</span>
                    </div>
                    <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-chevron-down text-gray-500 transform transition-transform" :class="openFaq === 4 ? 'rotate-180' : ''"></i>
                    </div>
                </button>
                <div x-show="openFaq === 4" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" class="px-8 pb-6">
                    <p class="text-gray-600 text-base leading-relaxed">Yes, you can cancel your subscription at any time with no cancellation fees. You'll continue to have access until the end of your current billing period.</p>
                </div>
            </div>

            <!-- FAQ Item 5 -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <button @click="openFaq = openFaq === 5 ? null : 5" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center mr-4">
                            <i class="fas fa-headset text-emerald-600 text-sm"></i>
                        </div>
                        <span class="font-semibold text-gray-900 text-lg">Do you offer customer support?</span>
                    </div>
                    <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-chevron-down text-gray-500 transform transition-transform" :class="openFaq === 5 ? 'rotate-180' : ''"></i>
                    </div>
                </button>
                <div x-show="openFaq === 5" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" class="px-8 pb-6">
                    <p class="text-gray-600 text-base leading-relaxed">Yes! We offer email support for all plans, with priority support for Pro and Business customers. Our team typically responds within 24 hours.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 py-20 lg:py-28 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-0 left-0 w-full h-full" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
        <div class="max-w-4xl mx-auto">
            <h2 class="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-white mb-6">
                Ready to Transform Your
                <span class="text-emerald-200">Business?</span>
            </h2>
            <p class="text-xl md:text-2xl text-emerald-100 mb-12 max-w-3xl mx-auto leading-relaxed">
                Join thousands of freelancers and small businesses who trust Freeligo to streamline their operations and grow their revenue.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                <a href="{{ route('register') }}" class="bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-emerald-50 transition-all duration-300 transform hover:scale-105 shadow-xl inline-flex items-center">
                    <i class="fas fa-rocket mr-3"></i>
                    Start Free Today
                </a>
                <a href="{{ route('contact') }}" class="border-2 border-white text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:bg-white hover:text-emerald-600 transition-all duration-300 inline-flex items-center">
                    <i class="fas fa-comments mr-3"></i>
                    Talk to Sales
                </a>
            </div>

            <!-- Trust Indicators -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-emerald-100">
                <div class="flex items-center justify-center">
                    <i class="fas fa-users text-2xl mr-3"></i>
                    <div class="text-left">
                        <div class="font-bold text-xl">10,000+</div>
                        <div class="text-sm opacity-90">Happy Users</div>
                    </div>
                </div>
                <div class="flex items-center justify-center">
                    <i class="fas fa-star text-2xl mr-3"></i>
                    <div class="text-left">
                        <div class="font-bold text-xl">4.9/5</div>
                        <div class="text-sm opacity-90">User Rating</div>
                    </div>
                </div>
                <div class="flex items-center justify-center">
                    <i class="fas fa-shield-alt text-2xl mr-3"></i>
                    <div class="text-left">
                        <div class="font-bold text-xl">100%</div>
                        <div class="text-sm opacity-90">Secure</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
</x-frontend-layout>
