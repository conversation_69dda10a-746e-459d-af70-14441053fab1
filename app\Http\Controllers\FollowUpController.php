<?php

namespace App\Http\Controllers;

use App\Models\FollowUp;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FollowUpController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Auth::user()->followUps()->with(['invoice.client']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('method')) {
            $query->where('method', $request->method);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('message', 'like', "%{$search}%")
                  ->orWhereHas('invoice', function ($invoiceQuery) use ($search) {
                      $invoiceQuery->where('invoice_number', 'like', "%{$search}%")
                                  ->orWhereHas('client', function ($clientQuery) use ($search) {
                                      $clientQuery->where('name', 'like', "%{$search}%")
                                                 ->orWhere('company_name', 'like', "%{$search}%");
                                  });
                  });
            });
        }

        $followUps = $query->latest('scheduled_at')->paginate(15);

        // Get clients and invoices for filter dropdowns with optimized queries and limits
        $clients = Auth::user()->clients()->select('id', 'name', 'company_name')->limit(1000)->get();
        $invoices = Auth::user()->invoices()
            ->select('id', 'invoice_number', 'client_id')
            ->with('client:id,name,company_name')
            ->where('status', '!=', 'paid') // Only unpaid invoices for follow-ups
            ->latest()
            ->limit(500)
            ->get();

        // Calculate statistics
        $totalFollowups = Auth::user()->followUps()->count();
        $scheduledFollowups = Auth::user()->followUps()->where('status', 'scheduled')->count();
        $sentFollowups = Auth::user()->followUps()->where('status', 'sent')->count();
        $completedFollowups = Auth::user()->followUps()->where('status', 'completed')->count();

        return view('followups.index', compact(
            'followUps',
            'clients',
            'invoices',
            'totalFollowups',
            'scheduledFollowups',
            'sentFollowups',
            'completedFollowups'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $clients = Auth::user()->clients()->select('id', 'name', 'company_name')->limit(1000)->get();
        $invoices = Auth::user()->invoices()
                             ->select('id', 'invoice_number', 'client_id', 'total_amount', 'due_date', 'status')
                             ->where('status', '!=', 'paid')
                             ->with('client:id,name,company_name')
                             ->latest()
                             ->limit(500)
                             ->get();

        $selectedInvoice = null;
        if ($request->filled('invoice_id')) {
            $selectedInvoice = $invoices->where('id', $request->invoice_id)->first();
        }

        // For now, provide empty templates array since template selection is optional
        $templates = collect();

        return view('followups.create', compact('clients', 'invoices', 'selectedInvoice', 'templates'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'invoice_id' => 'required|exists:invoices,id',
            'type' => 'required|in:reminder,final_notice,legal_notice',
            'method' => 'required|in:email,whatsapp,phone',
            'message' => 'required|string',
            'scheduled_at' => 'required|date|after:now',
        ]);

        $validated['user_id'] = Auth::id();
        $validated['status'] = 'pending';

        FollowUp::create($validated);

        return redirect()->route('followups.index')
                        ->with('success', 'Follow-up scheduled successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(FollowUp $followUp)
    {
        $this->authorize('view', $followUp);
        $followUp->load(['invoice.client']);

        return view('followups.show', compact('followUp'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FollowUp $followUp)
    {
        $this->authorize('update', $followUp);

        if ($followUp->status === 'sent') {
            return redirect()->route('followups.show', $followUp)
                            ->with('error', 'Cannot edit sent follow-ups.');
        }

        $invoices = Auth::user()->invoices()
                             ->select('id', 'invoice_number', 'client_id', 'total_amount', 'due_date', 'status')
                             ->where('status', '!=', 'paid')
                             ->with('client:id,name,company_name')
                             ->latest()
                             ->limit(500)
                             ->get();

        // For now, provide empty templates array since template selection is optional
        $templates = collect();

        return view('followups.edit', compact('followUp', 'invoices', 'templates'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, FollowUp $followUp)
    {
        $this->authorize('update', $followUp);

        if ($followUp->status === 'sent') {
            return redirect()->route('followups.show', $followUp)
                            ->with('error', 'Cannot edit sent follow-ups.');
        }

        $validated = $request->validate([
            'invoice_id' => 'required|exists:invoices,id',
            'type' => 'required|in:reminder,final_notice,legal_notice',
            'method' => 'required|in:email,whatsapp,phone',
            'message' => 'required|string',
            'scheduled_at' => 'required|date|after:now',
        ]);

        $followUp->update($validated);

        return redirect()->route('followups.show', $followUp)
                        ->with('success', 'Follow-up updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FollowUp $followUp)
    {
        $this->authorize('delete', $followUp);

        if ($followUp->status === 'sent') {
            return redirect()->route('followups.index')
                            ->with('error', 'Cannot delete sent follow-ups.');
        }

        $followUp->delete();

        return redirect()->route('followups.index')
                        ->with('success', 'Follow-up deleted successfully.');
    }

    /**
     * Mark follow-up as sent.
     */
    public function markAsSent(FollowUp $followUp)
    {
        $this->authorize('update', $followUp);

        $followUp->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        return redirect()->back()
                        ->with('success', 'Follow-up marked as sent.');
    }

    /**
     * Get follow-up templates.
     */
    public function getTemplates(Request $request)
    {
        $type = $request->get('type');
        $method = $request->get('method');

        $templates = $this->getFollowUpTemplates($type, $method);

        return response()->json($templates);
    }

    /**
     * Get predefined follow-up templates.
     */
    private function getFollowUpTemplates($type, $method)
    {
        $templates = [
            'reminder' => [
                'email' => [
                    'subject' => 'Payment Reminder - Invoice {{invoice_number}}',
                    'message' => 'Dear {{client_name}},

This is a friendly reminder that payment for Invoice {{invoice_number}} dated {{invoice_date}} for ₹{{amount}} is due on {{due_date}}.

Please process the payment at your earliest convenience.

Thank you for your business.

Best regards,
{{business_name}}'
                ],
                'whatsapp' => [
                    'message' => 'Hi {{client_name}},

This is a reminder that payment for Invoice {{invoice_number}} (₹{{amount}}) is due on {{due_date}}.

Please let me know if you need any clarification.

Thanks!'
                ]
            ],
            'final_notice' => [
                'email' => [
                    'subject' => 'Final Notice - Overdue Payment Invoice {{invoice_number}}',
                    'message' => 'Dear {{client_name}},

This is a final notice regarding the overdue payment for Invoice {{invoice_number}} dated {{invoice_date}} for ₹{{amount}}.

The payment was due on {{due_date}} and is now {{days_overdue}} days overdue.

Please arrange immediate payment to avoid any further action.

Best regards,
{{business_name}}'
                ],
                'whatsapp' => [
                    'message' => 'Hi {{client_name}},

Final reminder: Invoice {{invoice_number}} (₹{{amount}}) is now {{days_overdue}} days overdue.

Please arrange immediate payment.

Thanks.'
                ]
            ]
        ];

        return $templates[$type][$method] ?? ['message' => ''];
    }
}
