<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class TimeEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'project_id',
        'task_id',
        'description',
        'start_time',
        'end_time',
        'duration_minutes',
        'hourly_rate',
        'is_billable',
        'is_running',
        'is_invoiced',
        'invoice_id',
        'notes',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'start_time' => 'datetime',
            'end_time' => 'datetime',
            'duration_minutes' => 'integer',
            'hourly_rate' => 'decimal:2',
            'is_billable' => 'boolean',
            'is_running' => 'boolean',
            'is_invoiced' => 'boolean',
            'metadata' => 'array',
        ];
    }

    /**
     * Append currency-related attributes to JSON.
     */
    protected $appends = [
        'formatted_earned_amount',
        'formatted_hourly_rate',
        'currency',
        'earned_amount',
    ];

    /**
     * Get the user that owns the time entry.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the project for the time entry.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the task for the time entry.
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * Get the invoice for the time entry.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Scope a query to only include running timers.
     */
    public function scopeRunning($query)
    {
        return $query->where('is_running', true);
    }

    /**
     * Scope a query to only include completed entries.
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_running', false)->whereNotNull('end_time');
    }

    /**
     * Scope a query to only include billable entries.
     */
    public function scopeBillable($query)
    {
        return $query->where('is_billable', true);
    }

    /**
     * Scope a query to only include uninvoiced entries.
     */
    public function scopeUninvoiced($query)
    {
        return $query->where('is_invoiced', false);
    }

    /**
     * Scope a query for entries within date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('start_time', [$startDate, $endDate]);
    }

    /**
     * Get duration in hours.
     */
    public function getDurationHoursAttribute(): float
    {
        return $this->duration_minutes ? $this->duration_minutes / 60 : 0;
    }

    /**
     * Get current duration for running timer.
     */
    public function getCurrentDurationAttribute(): int
    {
        if (!$this->is_running) {
            return $this->duration_minutes ?? 0;
        }

        return Carbon::parse($this->start_time)->diffInMinutes(now());
    }

    /**
     * Get current duration in hours for running timer.
     */
    public function getCurrentDurationHoursAttribute(): float
    {
        return $this->current_duration / 60;
    }

    /**
     * Get earned amount for this entry.
     */
    public function getEarnedAmountAttribute(): float
    {
        if (!$this->is_billable) {
            return 0.0;
        }

        $rate = $this->hourly_rate ?? 0;
        $hours = $this->duration_hours ?? 0;

        // Handle edge cases
        if ($rate <= 0 || $hours <= 0) {
            return 0.0;
        }

        return round($hours * $rate, 2);
    }

    /**
     * Stop the running timer.
     */
    public function stop(): void
    {
        if (!$this->is_running) {
            return;
        }

        $endTime = now();
        $durationMinutes = Carbon::parse($this->start_time)->diffInMinutes($endTime);

        $this->update([
            'end_time' => $endTime,
            'duration_minutes' => $durationMinutes,
            'is_running' => false,
        ]);

        // Update task actual hours if task is assigned
        if ($this->task) {
            $this->task->increment('actual_hours', $durationMinutes / 60);
        }
    }

    /**
     * Resume a stopped timer.
     */
    public function resume(): void
    {
        if ($this->is_running) {
            return;
        }

        // Stop any other running timers for this user
        static::where('user_id', $this->user_id)
            ->where('is_running', true)
            ->each(fn($entry) => $entry->stop());

        $this->update([
            'start_time' => now(),
            'end_time' => null,
            'duration_minutes' => null,
            'is_running' => true,
        ]);
    }

    /**
     * Start a new timer.
     */
    public static function startTimer(array $data): self
    {
        // Stop any running timers for this user
        static::where('user_id', $data['user_id'])
            ->where('is_running', true)
            ->each(fn($entry) => $entry->stop());

        return static::create(array_merge($data, [
            'start_time' => now(),
            'is_running' => true,
        ]));
    }

    /**
     * Get formatted duration string.
     */
    public function getFormattedDurationAttribute(): string
    {
        $minutes = $this->is_running ? $this->current_duration : $this->duration_minutes;

        if (!$minutes) {
            return '00:00';
        }

        $hours = floor($minutes / 60);
        $mins = $minutes % 60;

        return sprintf('%02d:%02d', $hours, $mins);
    }

    /**
     * Get elapsed time for running timer.
     */
    public function getElapsedTimeAttribute(): string
    {
        if (!$this->is_running) {
            return $this->getFormattedDurationAttribute();
        }

        return $this->getFormattedDurationAttribute();
    }

    /**
     * Calculate duration from start and end time.
     */
    public function calculateDuration(): void
    {
        if ($this->start_time && $this->end_time) {
            $this->duration_minutes = Carbon::parse($this->start_time)->diffInMinutes($this->end_time);
        }
    }

    /**
     * Get active timer for a user.
     */
    public static function getActiveTimer(int $userId): ?self
    {
        return static::where('user_id', $userId)
            ->where('is_running', true)
            ->first();
    }

    /**
     * Stop active timer for a user.
     */
    public static function stopActiveTimer(int $userId): ?self
    {
        $activeTimer = static::getActiveTimer($userId);

        if ($activeTimer) {
            $activeTimer->stop();
        }

        return $activeTimer;
    }

    /**
     * Get formatted earned amount with project currency.
     */
    public function getFormattedEarnedAmountAttribute(): string
    {
        if (!$this->project) {
            // Fallback formatting without project
            $amount = $this->earned_amount ?? 0;
            return '$' . number_format($amount, 2);
        }

        return $this->project->formatCurrency($this->earned_amount ?? 0);
    }

    /**
     * Get formatted hourly rate with project currency.
     */
    public function getFormattedHourlyRateAttribute(): string
    {
        if (!$this->project) {
            // Fallback formatting without project
            $rate = $this->hourly_rate ?? 0;
            return '$' . number_format($rate, 2);
        }

        return $this->project->formatCurrency($this->hourly_rate ?? 0);
    }

    /**
     * Get project currency.
     */
    public function getCurrencyAttribute(): string
    {
        return $this->project?->currency ?? 'USD';
    }


}
