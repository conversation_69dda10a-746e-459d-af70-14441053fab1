<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->string('currency', 3)->default('USD')->after('billing_type');
            $table->index('currency');

            // Add check constraint for valid currency codes
            DB::statement('ALTER TABLE projects ADD CONSTRAINT chk_currency_format CHECK (currency REGEXP "^[A-Z]{3}$")');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Drop check constraint first
            DB::statement('ALTER TABLE projects DROP CONSTRAINT IF EXISTS chk_currency_format');

            $table->dropIndex(['currency']);
            $table->dropColumn('currency');
        });
    }
};
