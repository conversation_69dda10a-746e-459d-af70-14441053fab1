<x-app-layout>
    <x-slot name="title">Automation Preferences</x-slot>
    <x-slot name="subtitle">Configure your notification and automation settings</x-slot>

    <div class="space-y-6">
        <!-- Header -->
        <div>
            <h3 class="text-lg font-medium text-gray-900">Notification Preferences</h3>
            <p class="text-sm text-gray-600">Customize how and when you receive automated notifications</p>
        </div>

        <!-- Preferences Form -->
        <form id="preferencesForm" class="space-y-6">
            @csrf
            @method('PUT')

            @if($preferences->count() > 0)
                @foreach($preferences as $type => $typePreferences)
                    <div class="card">
                        <div class="card-header">
                            <h4 class="text-lg font-medium text-gray-900 capitalize">{{ str_replace('_', ' ', $type) }}</h4>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                @foreach($typePreferences as $preference)
                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                        <div class="flex-1">
                                            <h5 class="font-medium text-gray-900">{{ $preference->name }}</h5>
                                            @if($preference->description)
                                                <p class="text-sm text-gray-600 mt-1">{{ $preference->description }}</p>
                                            @endif
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <!-- Email Toggle -->
                                            <div class="flex items-center">
                                                <label class="text-sm text-gray-700 mr-2">Email</label>
                                                <input type="checkbox" 
                                                       name="preferences[{{ $preference->id }}][email]"
                                                       value="1"
                                                       {{ $preference->email_enabled ? 'checked' : '' }}
                                                       class="form-checkbox h-4 w-4 text-primary-600">
                                            </div>
                                            
                                            <!-- SMS Toggle -->
                                            <div class="flex items-center">
                                                <label class="text-sm text-gray-700 mr-2">SMS</label>
                                                <input type="checkbox" 
                                                       name="preferences[{{ $preference->id }}][sms]"
                                                       value="1"
                                                       {{ $preference->sms_enabled ? 'checked' : '' }}
                                                       class="form-checkbox h-4 w-4 text-primary-600">
                                            </div>
                                            
                                            <!-- In-App Toggle -->
                                            <div class="flex items-center">
                                                <label class="text-sm text-gray-700 mr-2">In-App</label>
                                                <input type="checkbox" 
                                                       name="preferences[{{ $preference->id }}][in_app]"
                                                       value="1"
                                                       {{ $preference->in_app_enabled ? 'checked' : '' }}
                                                       class="form-checkbox h-4 w-4 text-primary-600">
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <!-- Default Preferences -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="text-lg font-medium text-gray-900">Default Notification Settings</h4>
                    </div>
                    <div class="card-body">
                        <div class="space-y-4">
                            <!-- Invoice Notifications -->
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex-1">
                                    <h5 class="font-medium text-gray-900">Invoice Reminders</h5>
                                    <p class="text-sm text-gray-600 mt-1">Get notified about overdue invoices and payment reminders</p>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">Email</label>
                                        <input type="checkbox" name="invoice_reminders_email" value="1" checked class="form-checkbox h-4 w-4 text-primary-600">
                                    </div>
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">SMS</label>
                                        <input type="checkbox" name="invoice_reminders_sms" value="1" class="form-checkbox h-4 w-4 text-primary-600">
                                    </div>
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">In-App</label>
                                        <input type="checkbox" name="invoice_reminders_in_app" value="1" checked class="form-checkbox h-4 w-4 text-primary-600">
                                    </div>
                                </div>
                            </div>

                            <!-- Contract Notifications -->
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex-1">
                                    <h5 class="font-medium text-gray-900">Contract Alerts</h5>
                                    <p class="text-sm text-gray-600 mt-1">Get notified about expiring contracts and renewals</p>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">Email</label>
                                        <input type="checkbox" name="contract_alerts_email" value="1" checked class="form-checkbox h-4 w-4 text-primary-600">
                                    </div>
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">SMS</label>
                                        <input type="checkbox" name="contract_alerts_sms" value="1" class="form-checkbox h-4 w-4 text-primary-600">
                                    </div>
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">In-App</label>
                                        <input type="checkbox" name="contract_alerts_in_app" value="1" checked class="form-checkbox h-4 w-4 text-primary-600">
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Notifications -->
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex-1">
                                    <h5 class="font-medium text-gray-900">Payment Updates</h5>
                                    <p class="text-sm text-gray-600 mt-1">Get notified about successful payments and failed transactions</p>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">Email</label>
                                        <input type="checkbox" name="payment_updates_email" value="1" checked class="form-checkbox h-4 w-4 text-primary-600">
                                    </div>
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">SMS</label>
                                        <input type="checkbox" name="payment_updates_sms" value="1" class="form-checkbox h-4 w-4 text-primary-600">
                                    </div>
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">In-App</label>
                                        <input type="checkbox" name="payment_updates_in_app" value="1" checked class="form-checkbox h-4 w-4 text-primary-600">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Save Button -->
            <div class="flex justify-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save mr-2"></i>
                    Save Preferences
                </button>
            </div>
        </form>
    </div>

    <script>
        document.getElementById('preferencesForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            fetch('/automation/preferences', {
                method: 'PUT',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    const successDiv = document.createElement('div');
                    successDiv.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4';
                    successDiv.innerHTML = '<i class="fas fa-check mr-2"></i>Preferences saved successfully!';
                    
                    const form = document.getElementById('preferencesForm');
                    form.parentNode.insertBefore(successDiv, form);
                    
                    // Remove success message after 3 seconds
                    setTimeout(() => {
                        successDiv.remove();
                    }, 3000);
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving preferences');
            });
        });
    </script>
</x-app-layout>
